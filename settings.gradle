pluginManagement {
    repositories {
        maven { url "https://maven.fabricmc.net/" }
        maven { url "https://maven.architectury.dev/" }
        maven { url "https://maven.minecraftforge.net/" }
        maven { url "https://maven.shedaniel.me/" }
        maven { url "https://maven.terraformersmc.com/releases/" }
        gradlePluginPortal()
    }
}

include("common")
include("fabric")
include("forge")

rootProject.name = "immersive-aircraft"
