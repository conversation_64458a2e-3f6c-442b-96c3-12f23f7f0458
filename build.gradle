buildscript {
    configurations.classpath {
        resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
    }
}

plugins {
    id "architectury-plugin" version "3.4-SNAPSHOT"
    id "dev.architectury.loom" version "1.5-SNAPSHOT" apply false
}

architectury {
    minecraft = rootProject.minecraft_version
}

subprojects {
    apply plugin: "dev.architectury.loom"

    loom {
        silentMojangMappingsLicense()
    }

    repositories {
        maven { url = 'https://maven.parchmentmc.org' }
        maven { url "https://maven.terraformersmc.com/releases/" }
        maven { url = "https://dvs1.progwml6.com/files/maven/" }
        maven { url "<https://maven.shedaniel.me/>" }
        maven { url "<https://maven.architectury.dev/>" }
        maven { url "https://nexus.resourcefulbees.com/repository/maven-public/" }
        maven { url "https://maven.teamresourceful.com/repository/maven-public/" }
        maven { url "https://cursemaven.com" }
        maven { url "https://maven.conczin.net/Artifacts" }
    }

    dependencies {
        minecraft "com.mojang:minecraft:${rootProject.architectury.minecraft}"
        mappings loom.layered() {
            officialMojangMappings()
            parchment("org.parchmentmc.data:parchment-${rootProject.minecraft_version}:${rootProject.parchment_version}@zip")
        }
    }
}


def getAppVersion = { ->
    try {
        def stdout = new ByteArrayOutputStream()
        exec {
            commandLine 'git', 'tag', '--points-at', 'HEAD'
            standardOutput = stdout
        }
        def tag = stdout.toString().trim()
        if (tag.isEmpty()) {
            return rootProject.minecraft_version + "-SNAPSHOT"
        }
        return tag
    } catch (Exception e) {
        println "Warning: ${e.message}, is git installed?"
        return rootProject.minecraft_version + "-1.2.2+1.20.1"
    }
} as Object


allprojects {
    apply plugin: "java"
    apply plugin: "architectury-plugin"
    apply plugin: "maven-publish"

    archivesBaseName = rootProject.archives_base_name
    version = getAppVersion()

    tasks.withType(JavaCompile) {
        options.encoding = "UTF-8"
        options.release = 17
    }

    java {
        withSourcesJar()
    }

    publishing {
        repositories {
            maven {
                name = "reposiliteRepository"
                url = uri("https://maven.conczin.net/Artifacts")
                credentials {
                    username = System.getenv("MAVEN_USERNAME")
                    password = System.getenv("MAVEN_TOKEN")
                }
            }
        }
    }

    // Define reusable exclusion patterns
    def exclusionPatterns = ['**/*.aup3', '**/.xdp-*', '**/*.kra', '**/*~*']

    // Apply to processResources
    processResources {
        exclusionPatterns.each { pattern ->
            exclude pattern
        }
    }

    // Apply to sourcesJar
    sourcesJar {
        exclusionPatterns.each { pattern ->
            exclude pattern
        }
    }
}