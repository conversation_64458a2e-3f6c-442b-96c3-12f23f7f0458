To honour the JVM settings for this build a single-use Daemon process will be forked. For more on this, please refer to https://docs.gradle.org/8.4/userguide/gradle_daemon.html#sec:disabling_the_daemon in the Gradle documentation.
Daemon will be stopped at the end of the build 

> Configure project :
Architect Plugin: 3.4.161
Architectury Loom: 1.5.391

> Task :forge:dependencies

------------------------------------------------------------
Project ':forge'
------------------------------------------------------------

runtimeClasspath - Runtime classpath of source set 'main'.
+--- dev.architectury:architectury-transformer:5.2.87
+--- project :common
+--- net.minecraftforge:javafmllanguage:1.20.1-47.0.3
+--- net.minecraftforge:lowcodelanguage:1.20.1-47.0.3
+--- net.minecraftforge:mclanguage:1.20.1-47.0.3
+--- net.minecraftforge:fmlcore:1.20.1-47.0.3
+--- net.minecraftforge.d8dc6ea2d2bbc0e5a144e0f80295a78acf9bed3bb37d4796cefbc423b81db143:fmlloader:1.20.1-47.0.3
+--- net.minecraftforge:coremods:5.0.1
+--- net.minecraftforge:forgespi:7.0.0
+--- net.minecraftforge:accesstransformers:8.0.4
+--- net.minecraftforge:eventbus:6.0.3
+--- cpw.mods:modlauncher:10.0.9
+--- cpw.mods:bootstraplauncher:1.1.2
+--- cpw.mods:securejarhandler:2.1.10
+--- org.openjdk.nashorn:nashorn-core:15.3
+--- org.ow2.asm:asm-commons:9.5
+--- net.minecraftforge:mergetool:1.1.5
+--- org.ow2.asm:asm-util:9.5
+--- org.ow2.asm:asm-analysis:9.5
+--- org.ow2.asm:asm-tree:9.5
+--- org.ow2.asm:asm:9.5
+--- org.antlr:antlr4:4.9.1
+--- org.antlr:antlr4-runtime:4.9.1
+--- net.minecraftforge:unsafe:0.2.0
+--- com.electronwill.night-config:toml:3.6.4
+--- com.electronwill.night-config:core:3.6.4
+--- net.minecraftforge:JarJarSelector:0.3.19
+--- net.minecraftforge:JarJarMetadata:0.3.19
+--- org.apache.maven:maven-artifact:3.8.5
+--- net.jodah:typetools:0.8.3
+--- net.minecrell:terminalconsoleappender:1.2.0 -> 1.3.0
|    +--- org.apache.logging.log4j:log4j-core:2.8.1 -> 2.19.0
|    |    \--- org.apache.logging.log4j:log4j-api:2.19.0
|    \--- org.jline:jline-reader:3.20.0
|         \--- org.jline:jline-terminal:3.20.0
+--- org.jline:jline-reader:3.12.1 -> 3.20.0 (*)
+--- org.jline:jline-terminal:3.12.1 -> 3.20.0
+--- dev.architectury:mixin-patched:0.8.5.12
+--- net.minecraftforge:JarJarFileSystems:0.3.19
+--- net.sf.jopt-simple:jopt-simple:5.0.4
+--- com.mojang:logging:1.1.1
+--- org.apache.logging.log4j:log4j-core:2.19.0 (*)
+--- org.apache.logging.log4j:log4j-slf4j2-impl:2.19.0
+--- org.apache.logging.log4j:log4j-api:2.19.0
+--- net.minecraftforge:srgutils:0.4.11
+--- org.codehaus.plexus:plexus-utils:3.3.0
+--- org.apache.commons:commons-lang3:3.12.0
+--- com.google.code.gson:gson:2.10
+--- com.google.guava:guava:31.1-jre
+--- com.machinezoo.noexception:noexception:1.7.1
+--- org.slf4j:slf4j-simple:1.7.30
+--- org.slf4j:slf4j-api:2.0.0 -> 2.0.1
+--- commons-io:commons-io:2.11.0
+--- org.antlr:ST4:4.3
+--- org.antlr:antlr-runtime:3.5.2
+--- org.abego.treelayout:org.abego.treelayout.core:1.0.3
+--- org.glassfish:javax.json:1.0.4
+--- com.ibm.icu:icu4j:61.1 -> 71.1
+--- com.google.guava:failureaccess:1.0.1
+--- com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava
+--- com.google.code.findbugs:jsr305:3.0.2
+--- org.checkerframework:checker-qual:3.12.0
+--- com.google.errorprone:error_prone_annotations:2.11.0
+--- com.google.j2objc:j2objc-annotations:1.3
+--- io.github.juuxel:unprotect:1.2.0
|    \--- org.apache.logging.log4j:log4j-api:2.11.2 -> 2.19.0
+--- dev.architectury:architectury-naming-service:2.0.9
+--- dev.architectury:architectury-mixin-remapper-service:2.0.9
+--- dev.architectury:mcp-annotations:2.0.9
+--- org.mariuszgromada.math:MathParser.org-mXparser:5.2.1
+--- net.fabricmc:dev-launch-injector:0.2.1+build.8
+--- net.minecrell:terminalconsoleappender:1.3.0 (*)
+--- com.github.oshi:oshi-core:6.2.2
+--- com.ibm.icu:icu4j:71.1
+--- com.mojang:authlib:4.0.43
+--- com.mojang:blocklist:1.0.10
+--- com.mojang:brigadier:1.1.8
+--- com.mojang:datafixerupper:6.0.8
+--- com.mojang:patchy:2.2.10
+--- com.mojang:text2speech:1.17.9
+--- commons-codec:commons-codec:1.15
+--- commons-logging:commons-logging:1.2
+--- io.netty:netty-buffer:4.1.82.Final
+--- io.netty:netty-codec:4.1.82.Final
+--- io.netty:netty-common:4.1.82.Final
+--- io.netty:netty-handler:4.1.82.Final
+--- io.netty:netty-resolver:4.1.82.Final
+--- io.netty:netty-transport-classes-epoll:4.1.82.Final
+--- io.netty:netty-transport-native-unix-common:4.1.82.Final
+--- io.netty:netty-transport:4.1.82.Final
+--- it.unimi.dsi:fastutil:8.5.9
+--- net.java.dev.jna:jna-platform:5.12.1
+--- net.java.dev.jna:jna:5.12.1
+--- org.apache.commons:commons-compress:1.21
+--- org.apache.httpcomponents:httpclient:4.5.13
+--- org.apache.httpcomponents:httpcore:4.4.15
+--- org.joml:joml:1.10.5
+--- org.lwjgl:lwjgl-glfw:3.3.1 -> 3.3.2
+--- org.lwjgl:lwjgl-jemalloc:3.3.1 -> 3.3.2
+--- org.lwjgl:lwjgl-openal:3.3.1 -> 3.3.2
+--- org.lwjgl:lwjgl-opengl:3.3.1 -> 3.3.2
+--- org.lwjgl:lwjgl-stb:3.3.1 -> 3.3.2
+--- org.lwjgl:lwjgl-tinyfd:3.3.1 -> 3.3.2
+--- org.lwjgl:lwjgl:3.3.1 -> 3.3.2
+--- org.slf4j:slf4j-api:2.0.1
+--- org.lwjgl:lwjgl-glfw:3.3.2
+--- org.lwjgl:lwjgl-jemalloc:3.3.2
+--- org.lwjgl:lwjgl-openal:3.3.2
+--- org.lwjgl:lwjgl-opengl:3.3.2
+--- org.lwjgl:lwjgl-stb:3.3.2
+--- org.lwjgl:lwjgl-tinyfd:3.3.2
+--- org.lwjgl:lwjgl:3.3.2
+--- net.minecraft:forge-1.20.1-47.0.3-minecraft-merged-d46afd33b2:1.20.1-loom.mappings.1_20_1.layered+hash.1900670477-v2-forge-1.20.1-47.0.3
+--- io.netty:netty-transport-native-epoll:4.1.82.Final
+--- loom_mappings_1_20_1_layered_hash_1900670477_v2_forge_1_20_1_47_0_3_forge.me.shedaniel:RoughlyEnoughItems-forge:12.0.684
+--- loom_mappings_1_20_1_layered_hash_1900670477_v2_forge_1_20_1_47_0_3_forge.me.shedaniel.cloth:cloth-config-forge:11.0.99
+--- loom_mappings_1_20_1_layered_hash_1900670477_v2_forge_1_20_1_47_0_3_forge.dev.architectury:architectury-forge:9.0.7
+--- loom_mappings_1_20_1_layered_hash_1900670477_v2_forge_1_20_1_47_0_3_forge.blue.endless:jankson:1.2.0
+--- loom_mappings_1_20_1_layered_hash_1900670477_v2_forge_1_20_1_47_0_3_forge.com.moandjiezana.toml:toml4j:0.7.2
+--- loom_mappings_1_20_1_layered_hash_1900670477_v2_forge_1_20_1_47_0_3_forge.org.yaml:snakeyaml:1.27
\--- loom_mappings_1_20_1_layered_hash_1900670477_v2_forge_1_20_1_47_0_3_forge.com.google.code.gson:gson:2.8.1

(*) - Indicates repeated occurrences of a transitive dependency subtree. Gradle expands transitive dependency subtrees only once per project; repeat occurrences only display the root of the subtree, followed by this annotation.

A web-based, searchable dependency report is available by adding the --scan option.

Deprecated Gradle features were used in this build, making it incompatible with Gradle 9.0.

You can use '--warning-mode all' to show the individual deprecation warnings and determine if they come from your own scripts or plugins.

For more on this, please refer to https://docs.gradle.org/8.4/userguide/command_line_interface.html#sec:command_line_warnings in the Gradle documentation.

BUILD SUCCESSFUL in 14s
1 actionable task: 1 executed
