plugins {
    id "com.github.johnrengelman.shadow" version "7.1.2"
}

architectury {
    platformSetupLoomIde()
    forge()
}

loom {
    accessWidenerPath = project(":common").loom.accessWidenerPath

    forge {
        convertAccessWideners = true
        extraAccessWideners.add loom.accessWidenerPath.get().asFile.name

        mixinConfig "immersive_aircraft.mixins.json"
    }
}

configurations {
    common
    shadowCommon // Don't use shadow from the shadow plugin because we don't want IDEA to index this.
    compileClasspath.extendsFrom common
    runtimeClasspath.extendsFrom common
    developmentForge.extendsFrom common
}

dependencies {
    forge "net.minecraftforge:forge:${rootProject.minecraft_version}-${rootProject.forge_version}"

    forgeRuntimeLibrary 'org.mariuszgromada.math:MathParser.org-mXparser:5.2.1'
    include 'org.mariuszgromada.math:MathParser.org-mXparser:5.2.1'

    common(project(path: ":common", configuration: "namedElements")) { transitive false }
    shadowCommon(project(path: ":common", configuration: "transformProductionForge")) { transitive false }

    modCompileOnlyApi("mezz.jei:jei-${minecraft_version}-forge-api:${jei_version}")

    modCompileOnly "me.shedaniel:RoughlyEnoughItems-api-forge:$rei_version"
    modCompileOnly "me.shedaniel:RoughlyEnoughItems-default-plugin-forge:$rei_version"

    // Required to run JEI
    // modRuntimeOnly("mezz.jei:jei-${minecraft_version}-forge:${jei_version}")

    // Required to run JEI
    modRuntimeOnly "me.shedaniel:RoughlyEnoughItems-forge:$rei_version"

    // Create mod and Create Big Cannons dependencies
    modCompileOnly "curse.maven:create-328085:${create_version}"
    modCompileOnly "curse.maven:create-big-cannons-646668:${create_big_cannons_version}"

    // modCompileOnly "earth.terrarium.adastra:ad_astra-forge-$minecraft_version:${ad_astra_version}"
    // modRuntimeOnly "earth.terrarium.adastra:ad_astra-forge-$minecraft_version:${ad_astra_version}"
}

processResources {
    inputs.property "version", project.version

    filesMatching("META-INF/mods.toml") {
        expand "version": project.version
    }
}

shadowJar {
    exclude "fabric.mod.json"
    exclude "architectury.common.json"

    configurations = [project.configurations.shadowCommon]
    archiveClassifier = "dev-shadow"
}

remapJar {
    input.set shadowJar.archiveFile
    dependsOn shadowJar
}

sourcesJar {
    def commonSources = project(":common").sourcesJar
    dependsOn commonSources
    from commonSources.archiveFile.map { zipTree(it) }
}

components.java {
    withVariantsFromConfiguration(project.configurations.shadowRuntimeElements) {
        skip()
    }
}

// Update mods.toml with the new versions automatically
// Not using processResources because it is easier to do this manually, and see it reflected immediately
afterEvaluate {
    file("src/main/resources/META-INF/mods.toml").withOutputStream {
        it << file("mods.toml").text
                .replaceAll("@LOADER_MAJOR@", rootProject.forge_version.split("\\.")[0])
                .replaceAll("@MINECRAFT_VERSION@", rootProject.architectury.minecraft)
                .replaceAll("@FORGE_VERSION@", rootProject.forge_version)
    }
}

publishing {
    publications {
        mavenCommon(MavenPublication) {
            artifactId = rootProject.archives_base_name
            groupId = rootProject.group_id
            version = version + "+forge"
            from components.java
        }
    }
}
