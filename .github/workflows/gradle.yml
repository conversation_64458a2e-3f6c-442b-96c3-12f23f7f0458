name: Java CI with Gradle

on:
  push:
    branches-ignore:
      - 'l10n*'
    tags:
      - '*'
jobs:
  maven:
    runs-on: ubuntu-latest
    env:
      MAVEN_USERNAME: ${{ secrets.MAVEN_USERNAME }}
      MAVEN_TOKEN: ${{ secrets.MAVEN_TOKEN }}
    steps:
      - name: Checkout Commit
        uses: actions/checkout@v2
      - name: Fetch Tags
        run: git fetch --unshallow --tags
      - name: Set up JDK 17
        uses: actions/setup-java@v1
        with:
          java-version: 17
      - name: Grant execute permission for gradlew
        run: chmod +x gradlew
      - name: Publish to Maven
        run: ./gradlew publish

  build:
    runs-on: ubuntu-latest
    env:
      MODRINTH_TOKEN: ${{ secrets.MR_API_TOKEN }}
      CURSEFORGE_TOKEN: ${{ secrets.CF_API_TOKEN }}
      MAVEN_USERNAME: ${{ secrets.MAVEN_USERNAME }}
      MAVEN_TOKEN: ${{ secrets.MAVEN_TOKEN }}
    if: startsWith(github.ref, 'refs/tags/')
    steps:
      - name: Checkout Commit
        uses: actions/checkout@v2
      - name: Fetch Tags
        run: git fetch --unshallow --tags
      - name: Set up JDK 17
        uses: actions/setup-java@v1
        with:
          java-version: 17
      - name: Grant execute permission for gradlew
        run: chmod +x gradlew
      - name: Build with Gradle
        run: ./gradlew build
      - name: Publish to Maven
        run: ./gradlew publish
      - name: Get tag
        if: ${{ success() && startsWith(github.ref, 'refs/tags/') }}
        id: tag
        uses: dawidd6/action-get-tag@v1
      - uses: jungwinter/split@v2
        id: split
        with:
          separator: "+"
          msg: ${{steps.tag.outputs.tag}}
      - name: "Rename Fabric files"
        run: mv "fabric/build/libs/immersive_aircraft-${{steps.tag.outputs.tag}}.jar" "fabric/build/libs/immersive_aircraft-${{steps.tag.outputs.tag}}-fabric.jar"
      - name: "Rename Forge files"
        run: mv "forge/build/libs/immersive_aircraft-${{steps.tag.outputs.tag}}.jar" "forge/build/libs/immersive_aircraft-${{steps.tag.outputs.tag}}-forge.jar"
      - name: "Upload Fabric"
        uses: Kir-Antipov/mc-publish@v3.3.0
        if: ${{ success() && startsWith(github.ref, 'refs/tags/') }}
        with:
          modrinth-id: x3HZvrj6
          modrinth-token: ${{ env.MODRINTH_TOKEN }}
          curseforge-id: 666014
          curseforge-token: ${{ env.CURSEFORGE_TOKEN }}

          name: "[Fabric ${{steps.split.outputs._1}}] Immersive Aircraft - ${{steps.split.outputs._0}}"
          changelog-file: changelog.md
          version-type: release
          files: "fabric/build/libs/immersive_aircraft-${{steps.tag.outputs.tag}}-fabric.jar"
          loaders: |
            fabric
          game-versions: |
            ${{steps.split.outputs._1}}
          java: |
            Java 17
          dependencies: |
            fabric-api | required | *

          fail-mode: skip
      - name: "Upload Forge"
        uses: Kir-Antipov/mc-publish@v3.3.0
        if: ${{ success() && startsWith(github.ref, 'refs/tags/') }}
        with:
          modrinth-id: x3HZvrj6
          modrinth-token: ${{ env.MODRINTH_TOKEN }}
          curseforge-id: 666014
          curseforge-token: ${{ env.CURSEFORGE_TOKEN }}

          name: "[Forge ${{steps.split.outputs._1}}] Immersive Aircraft - ${{steps.split.outputs._0}}"
          changelog-file: changelog.md
          version-type: release
          files: "forge/build/libs/immersive_aircraft-${{steps.tag.outputs.tag}}-forge.jar"
          loaders: |
            forge
          game-versions: |
            ${{steps.split.outputs._1}}
          java: |
            Java 17

          fail-mode: skip