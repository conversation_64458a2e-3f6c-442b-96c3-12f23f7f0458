package immersive_aircraft.entity;

import immersive_aircraft.config.Config;
import immersive_aircraft.item.upgrade.VehicleStat;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.core.BlockPos;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.phys.Vec3;
import org.joml.Vector3f;

/**
 * Implements airplane like physics properties and accelerated towards
 */
public abstract class AirplaneEntity extends AircraftEntity {
    private int time;
    private boolean isFlying;
    public AirplaneEntity(EntityType<? extends AircraftEntity> entityType, Level world, boolean canExplodeOnCrash) {
        super(entityType, world, canExplodeOnCrash);
        time = 0;
        if (!isNearGround()) {
            isFlying = true;
        } else {
            isFlying = false;
        }
    }

    @Override
    protected boolean useAirplaneControls() {
        return true;
    }

    @Override
    protected float getGravity() {
        Vector3f direction = this.getForwardDirection();
        float speed = (float) getDeltaMovement().length() * (1.0f - Math.abs(direction.y));

//        System.out.println(time);

        // Calculate if aircraft is in stall condition
        float mass = getProperties().get(VehicleStat.MASS);
        float lift = getProperties().get(VehicleStat.LIFT);
        float minSpeed = (float) (((1.75*mass) / (lift * 10.0f)) * 0.15f);
        System.out.println(minSpeed);
        boolean isStalling = !isNearGround() && speed < minSpeed;

        // Reduce lift factor during stall
        float liftFactor = isStalling ? 0.3f : 1.5f; // Much less lift when stalling
        float extraGravity = 0;
        if (isStalling) {
            extraGravity = 0.5f;
        }
        // Calculate effective takeoff time based on acceleration upgrades
        float baseTimeRequired = Config.getInstance().timeRequiredForTakeOff;
        float accelerationUpgrade = getProperties().get(VehicleStat.ACCELERATION);

        // If player has acceleration upgrade (like improved landing gear), reduce takeoff time by half
        float effectiveTimeRequired = baseTimeRequired;
        if (accelerationUpgrade > 1.0f) {
            effectiveTimeRequired = baseTimeRequired * 0.5f; // Reduce by half when upgrade is present
        }


        if (time >= effectiveTimeRequired) {
            isFlying = true;
            time = 0;

//            System.out.println(Math.max(0.0f, extraGravity + 1.0f - speed * liftFactor + (0.125f * mass * liftFactor)) * super.getGravity());

        }
//        System.out.println(time + " " + isFlying);
        if (isFlying) {
            return Math.max(0.0f, extraGravity + 1.0f - speed * liftFactor + (0.125f * mass * liftFactor)) * super.getGravity();
        }
        return -0.08f;
    }
    @Override
    public void tick() {
        super.tick();
        super.updateController();
        applyStallEffect();
        if (!isFlying && this.getEnginePower() >= 0.8f && this.getSpeedVector().length() > 0.0) {
//            if (!isNearGround() && this.getTickSinceLaunch() % 20 == 0) {
//                time++;
//            } else if (isNearGround()) {
                time++;
//            }
        }
//        if (getEngineTarget() < 0.1f && isNearGround()) {
//            applyFriction();
//        }
        Vector3f direction = getForwardDirection();
        // speed ;
        float thrust = (float) ((float) (getEnginePower() + getDeltaMovement().length())) * 0.03188831598f;//(float) (Math.pow(getEnginePower(), 2.0) * getProperties().get(VehicleStat.ENGINE_SPEED));
        // accelerate
//
        if (isFlying && isNearGround() && getEnginePower() < 0.1f) {
            this.isFlying = false;
        }
        setDeltaMovement(getDeltaMovement().scale(getBrakeFactor()).add(toVec3d(direction.mul(thrust))));
    }
    @Override
    public void setInputs(float x, float y, float z) {

        this.movementX = x;
        float speed = (float) getDeltaMovement().length();

        // Calculate minimum speed based on aircraft properties
        float mass = getProperties().get(VehicleStat.MASS);
        float lift = getProperties().get(VehicleStat.LIFT);
        float minSpeed = (float) (((1/75*mass) / (lift * 10.0f)) * 0.15f);
        boolean isStalling = !isNearGround() && speed < minSpeed;
        if (this.isFlying) {
            if (!isStalling) {
                this.movementZ = z;
            } else {
                if (isFlying) {
                    this.movementZ = z / 2;
                }
            }
        }

        this.movementY = y;

    }
    protected float getBrakeFactor() {
        if (this.getEnginePower() > 0.1f) {
            return 0.95f;
        }
        return 0.965f;
    }
    @Override
    public boolean isAlwaysTicking() {
        return true;
    }

    /**
     * Check if the entity is within 1 block of the ground
     */
    private boolean isNearGround() {
        BlockPos pos = blockPosition();
        // Check blocks below the entity within 1 block distance
        for (int y = 0; y <= 2; y++) {
            BlockPos checkPos = pos.below(y);
            if (!level().getBlockState(checkPos).isAir()) {
                return true;
            }
        }
        return false;
    }

    /**
     * Apply nose-down effect when aircraft is going too slow
     */
    private void applyStallEffect() {

        if (isNearGround()) {
            return; // Don't apply stall effect near ground
        }

        float speed = (float) getDeltaMovement().length();

        // Calculate minimum speed based on aircraft properties
        float mass = getProperties().get(VehicleStat.MASS);
        float lift = getProperties().get(VehicleStat.LIFT);
        float minSpeed = (float) (1.75*((mass) / (lift * 10.0f)) * 0.15f);
        // If going too slow, pitch nose down
        if (getXRot() > 90.0f) {
            setDeltaMovement(getDeltaMovement().scale(1.02f)); // Increase speed slightly to crash
        } else {
            if (speed < minSpeed) {


                float pitchDownForce = (minSpeed - speed) * 2.0f; // Stronger effect the slower you go

                setXRot(getXRot() + pitchDownForce);
            }
        }


    }

    @Override
    protected void updateController() {
        if (!isVehicle()) {
            return;
        }

        // Apply nose-down effect when going too slow

        super.updateController();

        // engine maintenance - keep engine running once started (but don't auto-start)
        // Engine will only turn off if components are removed, not from lack of input

        // manual engine control - space to start/increase, shift to reduce (only on ground)
        if (movementY != 0) {
            // Slower engine spool down when cutting power
            float engineChangeRate = movementY > 0 ? 0.1f : 0.03f; // Slower when reducing power

            // Prevent lowering engine power while airborne (safety feature)

                // Allow engine changes when near ground or increasing power
            setEngineTarget(Math.max(0.0f, Math.min(1.0f, getEngineTarget() + engineChangeRate * movementY)));

            if (movementY < 0) {
//                setDeltaMovement(getDeltaMovement().scale(getBrakeFactor()));
            }
        }

        // get the direction

    }

    @Override
    public boolean shouldRenderAtSqrDistance(double distance) {
        // Force 1000 block render distance for airplanes
        double d = 1000.0 * getViewScale();
        return distance < d * d;
    }

    /**
     * Check if the rudder is currently being used
     * @return true if rudder input is active
     */
    public boolean isRudderBeingUsed() {
        return Math.abs(movementX) > 0.01f;
    }

    /**
     * Get the current rudder input value
     * @return rudder input from -1.0 (full left) to 1.0 (full right)
     */
    public float getRudderInput() {
        return movementX;
    }

    /**
     * Get the smoothed rudder input value
     * @return smoothed rudder input from -1.0 (full left) to 1.0 (full right)
     */
    public float getRudderInputSmooth() {
        return pressingInterpolatedX.getSmooth();
    }
}
