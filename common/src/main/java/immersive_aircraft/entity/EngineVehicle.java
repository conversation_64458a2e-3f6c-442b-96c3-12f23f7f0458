package immersive_aircraft.entity;

import immersive_aircraft.Sounds;
import immersive_aircraft.cobalt.network.NetworkHandler;
import immersive_aircraft.config.Config;
import immersive_aircraft.entity.inventory.VehicleInventoryDescription;
import immersive_aircraft.entity.inventory.slots.SlotDescription;
import immersive_aircraft.item.upgrade.VehicleStat;
import immersive_aircraft.network.c2s.EnginePowerMessage;
import immersive_aircraft.resources.bbmodel.BBAnimationVariables;
import immersive_aircraft.util.InterpolatedFloat;
import immersive_aircraft.util.Utils;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.ChatFormatting;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.Vec3;
import org.jetbrains.annotations.NotNull;
import org.joml.Matrix3f;
import org.joml.Matrix4f;
import org.joml.Vector3f;
import org.joml.Vector4f;

import java.util.List;

/**
 * Simulated engine behavior
 */
public abstract class EngineVehicle extends InventoryVehicleEntity {
    protected static final EntityDataAccessor<Float> ENGINE = SynchedEntityData.defineId(EngineVehicle.class, EntityDataSerializers.FLOAT);
    private static final EntityDataAccessor<Float> UTILIZATION = SynchedEntityData.defineId(EngineVehicle.class, EntityDataSerializers.FLOAT);
    private static final EntityDataAccessor<Boolean> LOW_ON_FUEL = SynchedEntityData.defineId(EngineVehicle.class, EntityDataSerializers.BOOLEAN);

    public final InterpolatedFloat engineRotation = new InterpolatedFloat();
    public final InterpolatedFloat enginePower = new InterpolatedFloat(20.0f);
    public float engineSpinUpStrength = 0.0f;
    public float engineSound = 0.0f;



    protected enum FuelState {
        NEVER,
        EMPTY,
        FUELED,
        LOW
    }

    FuelState lastFuelState = FuelState.NEVER;

    // New fuel system: 8 buckets capacity per fuel tank, 5 minutes per bucket
    public static final int FUEL_PER_TANK = 8 * 6000; // 8 buckets × 1500 units per bucket = 12000 units per tank
    public static final int LOW_FUEL = 1500; // Low fuel when less than 1 bucket worth (5 minutes) remaining

    private int fuel;

    /**
     * Gets the maximum fuel capacity based on the number of fuel tanks installed
     */
    public int getFuelCapacity() {
        if (this instanceof InventoryVehicleEntity) {
            InventoryVehicleEntity inventoryVehicle = (InventoryVehicleEntity) this;
            int fuelTankCount = inventoryVehicle.getFuelTankCount();
            return (1 + fuelTankCount) * FUEL_PER_TANK; // At least 1 tank worth even if no tanks installed
        }
        return FUEL_PER_TANK; // Default to 1 tank capacity
    }

    public enum GUI_STYLE {
        NONE,
        ENGINE
    }

    public GUI_STYLE getGuiStyle() {
        return GUI_STYLE.ENGINE;
    }

    public EngineVehicle(EntityType<? extends EngineVehicle> entityType, Level world, boolean canExplodeOnCrash) {
        super(entityType, world, canExplodeOnCrash);

        fuel = 0;
    }

    protected SoundEvent getEngineStartSound() {
        return Sounds.ENGINE_START.get();
    }

    protected SoundEvent getEngineSound() {
        return Sounds.PROPELLER.get();
    }

    protected float getEngineVolume() {
        return 0.25f;
    }

    protected float getEnginePitch() {
        return 1.0f;
    }

    protected float getEngineReactionSpeed() {
        return 20.0f;
    }

    public boolean worksUnderWater() {
        return false;
    }

    @Override
    protected void defineSynchedData() {
        super.defineSynchedData();

        entityData.define(ENGINE, 0.0f);
        entityData.define(UTILIZATION, 0.0f);
        entityData.define(LOW_ON_FUEL, false);
    }

    @Override
    public void tick() {
        super.tick();

        // adapt engine reaction time
        enginePower.setSteps(getEngineReactionSpeed() / getProperties().get(VehicleStat.ACCELERATION));

        // spin up the engine
        enginePower.update(getEngineTarget() * (isInWater() && !worksUnderWater() ? 0.1f : 1.0f));

        // simulate spin up
        engineSpinUpStrength = Math.max(0.0f, engineSpinUpStrength + enginePower.getDiff() - 0.01f);

        // rotate propeller
        if (level().isClientSide()) {
            engineRotation.update((engineRotation.getValue() + getPropellerSpeed()) % 1000);
        }

        // shutdown - only if radial engine is removed, not when pilot leaves
        if (((!hasRadialEngine() && !isPilotCreative())) && getEngineTarget() > 0) {
            // Gradual engine shutdown instead of immediate cutoff to prevent sudden stops
            float currentTarget = getEngineTarget();
            float shutdownRate = 0.02f; // Gradual shutdown over ~50 ticks (2.5 seconds)
            setEngineTarget(Math.max(0.0f, currentTarget - shutdownRate));
        }


        // Engine sounds
        if (level().isClientSide) {
            engineSound += getEnginePower() * 0.25f;
            if (engineSound > 1.0f) {
                engineSound--;
                if (isFuelLow()) {
                    engineSound -= random.nextInt(2);
                }
                level().playLocalSound(getX(), getY() + getBbHeight() * 0.5, getZ(), getEngineSound(), getSoundSource(), Math.min(1.0f, getEngineVolume() + engineSpinUpStrength), (random.nextFloat() * 0.1f + 0.95f) * getEnginePitch(), false);
            }
        }

        // Fuel
        if (fuel > 0 && !level().isClientSide) {
            float consumption = getFuelConsumption();
            consumeFuel(consumption);
        }

        // Refuel
        if (isVehicle()) {
            if (!level().isClientSide()) {


                // Fuel notification
                if (getControllingPassenger() instanceof ServerPlayer player) {
                    float utilization = getFuelUtilization();
                    if (utilization > 0 && isFuelLow()) {
                        if (lastFuelState != FuelState.LOW) {
                            player.displayClientMessage(Component.translatable("immersive_aircraft." + getFuelType() + ".low"), true);
                            lastFuelState = FuelState.LOW;
                        }
                    } else if (utilization > 0) {
                        lastFuelState = FuelState.FUELED;
                    } else {
                        if (lastFuelState != FuelState.EMPTY) {
                            player.displayClientMessage(Component.translatable("immersive_aircraft." + getFuelType() + "." + (lastFuelState == FuelState.FUELED ? "out" : "none")), true);
                            lastFuelState = FuelState.EMPTY;
                        }
                    }
                }
            }
        } else {
            lastFuelState = FuelState.NEVER;
        }
    }

    public void consumeFuel(float consumption) {
        fuel -= consumption;
    }

    public float getPropellerSpeed() {
        return getEnginePower();
    }

    public boolean isFuelLow() {
        if (!Config.getInstance().burnFuelInCreative && isPilotCreative()) {
            return false;
        }

        return fuel <= LOW_FUEL;

    }

    public String getFuelType() {
        return "fuel";
    }

    public float getFuelConsumption() {
        return getEngineTarget() * getProperties().get(VehicleStat.FUEL) * Config.getInstance().fuelConsumption;
    }

    public boolean refuel(int i) {
        int maxCapacity = getFuelCapacity();
        if (maxCapacity >= i + this.fuel) {
            this.fuel += i;
            return true;
        }

        return false;
    }

//    private void refuel() {
//        refuel();
//    }



    public float getEnginePower() {
//        System.out.println((float) (enginePower.getSmooth() * Math.sqrt(getFuelUtilization())));
        return (float) (enginePower.getSmooth() * Math.sqrt(getFuelUtilization()));
    }

    public float getEngineTarget() {
        return entityData.get(ENGINE);
    }

    public void setEngineTarget(float engineTarget) {
        // Prevent turning off engine while airborne for safety
//        if (engineTarget == 0 && !onGround() && getEngineTarget() > 0) {
//            // Display safety warning to pilot
//            if (getControllingPassenger() instanceof net.minecraft.server.level.ServerPlayer player) {
//                player.displayClientMessage(
//                    net.minecraft.network.chat.Component.translatable("immersive_aircraft.engine_safety_warning")
//                        .withStyle(net.minecraft.ChatFormatting.RED),
//                    true
//                );
//            }
//            return; // Don't allow engine shutdown while airborne
//        }
        if (this.getControllingPassenger() instanceof Player player) {
            if (this.hasRadialEngine() && (getFuelUtilization() > 0 || engineTarget == 0)) { //(engineTarget >= entityData.get(ENGINE)) &&
                if (level().isClientSide) {
                    if (getEngineTarget() != engineTarget) {
                        NetworkHandler.sendToServer(new EnginePowerMessage(engineTarget));
                    }
                    if (getFuelUtilization() > 0 && getEngineTarget() == 0.0 && engineTarget > 0) {
                        level().playLocalSound(getX(), getY() + getBbHeight() * 0.5, getZ(), getEngineStartSound(), getSoundSource(), 1.5f, getEnginePitch(), false);
                    }
                }
                entityData.set(ENGINE, engineTarget);
            }
        }
    }

    public float getFuelUtilization() {
        if (Config.getInstance().fuelConsumption == 0) {
            return 1.0f;
        }
        if (!Config.getInstance().burnFuelInCreative && isPilotCreative()) {
            return 1.0f;
        }
        if (level().isClientSide) {
            return entityData.get(UTILIZATION);
        } else {

            float utilization = (float) (isFuelLow() ? 0.75f : 1.0f);
            entityData.set(UTILIZATION, utilization);
            return utilization;
        }
    }



    @Override
    public boolean canTurnOnEngine(Entity pilot) {
        // First check if it's a player
        if (!(pilot instanceof Player player)) {
            return false;
        }

        // Exception for quadcopters - they don't need radial engines or fuel tanks
        if (this instanceof immersive_aircraft.entity.QuadrocopterEntity) {
            return true;
        }

        // Check if radial engine is installed
        if (!hasRadialEngine()) {
            // Display error message to player
            player.displayClientMessage(
                net.minecraft.network.chat.Component.translatable("immersive_aircraft.radial_engine_required")
                    .withStyle(net.minecraft.ChatFormatting.RED),
                true
            );
            return false;
        }

        // Check if fuel tank is installed
//        if (!hasFuelTank()) {
//            // Display error message to player
//            player.displayClientMessage(
//                net.minecraft.network.chat.Component.translatable("immersive_aircraft.fuel_tank_required")
//                    .withStyle(net.minecraft.ChatFormatting.RED),
//                true
//            );
//            return false;
//        }

        return true;
    }

    public void emitSmokeParticle(float x, float y, float z, float nx, float ny, float nz) {
        if (!isWithinParticleRange() || !level().isClientSide) {
            return;
        }

        Matrix4f transform = getVehicleTransform();
        Matrix3f normalTransform = getVehicleNormalTransform();

        float power = getEnginePower();
        if (power > 0.05) {
            for (int i = 0; i < 1 + engineSpinUpStrength * 4; i++) {
                Vector4f p = transformPosition(transform, x, y, z);
                Vector3f vel = transformVector(normalTransform, nx, ny, nz);
                Vec3 velocity = getDeltaMovement();
                if (random.nextFloat() < engineSpinUpStrength * 0.1) {
                    vel.mul(0.5f);
                    level().addParticle(ParticleTypes.SMALL_FLAME, p.x(), p.y(), p.z(), vel.x() + velocity.x, vel.y() + velocity.y, vel.z() + velocity.z);
                } else {
                    level().addParticle(ParticleTypes.SMOKE, p.x, p.y, p.z, vel.x + velocity.x, vel.y + velocity.y, vel.z + velocity.z);
                }
            }
        }
    }

    @Override
    protected void addAdditionalSaveData(@NotNull CompoundTag tag) {
        super.addAdditionalSaveData(tag);

        tag.putInt("Fuel", fuel);
    }

    @Override
    protected void readAdditionalSaveData(@NotNull CompoundTag tag) {
        super.readAdditionalSaveData(tag);

        fuel = tag.getInt("Fuel");
    }

    @Override
    public void setAnimationVariables(float tickDelta) {
        super.setAnimationVariables(tickDelta);

        BBAnimationVariables.set("engine_rotation", engineRotation.getSmooth(tickDelta));
    }
}
