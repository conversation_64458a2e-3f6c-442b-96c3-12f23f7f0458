package immersive_aircraft.network.s2c;

import immersive_aircraft.cobalt.network.Message;
import immersive_aircraft.entity.InventoryVehicleEntity;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.player.Player;

public class TrailColorMessage extends Message {
    private final int vehicleId;
    private final float[] trailColor; // RGB values or null

    public TrailColorMessage(int vehicleId, float[] trailColor) {
        this.vehicleId = vehicleId;
        this.trailColor = trailColor;
    }

    public TrailColorMessage(FriendlyByteBuf buffer) {
        this.vehicleId = buffer.readInt();
        boolean hasColor = buffer.readBoolean();
        if (hasColor) {
            this.trailColor = new float[3];
            this.trailColor[0] = buffer.readFloat();
            this.trailColor[1] = buffer.readFloat();
            this.trailColor[2] = buffer.readFloat();
        } else {
            this.trailColor = null;
        }
    }

    @Override
    public void encode(FriendlyByteBuf buffer) {
        buffer.writeInt(vehicleId);
        if (trailColor != null && trailColor.length >= 3) {
            buffer.writeBoolean(true);
            buffer.writeFloat(trailColor[0]);
            buffer.writeFloat(trailColor[1]);
            buffer.writeFloat(trailColor[2]);
        } else {
            buffer.writeBoolean(false);
        }
    }

    @Override
    public void receive(Player player) {
        Entity entity = player.level().getEntity(vehicleId);
        if (entity instanceof InventoryVehicleEntity vehicle) {
            vehicle.setClientTrailColor(trailColor);
        }
    }
}
