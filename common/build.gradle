architectury {
    common(rootProject.enabled_platforms.split(","))
}

loom {
    accessWidenerPath = file("src/main/resources/immersive_aircraft.accessWidener")
}

dependencies {
    // We depend on fabric loader here to use the fabric @Environment annotations
    // Do NOT use other classes from fabric loader
    modImplementation "net.fabricmc:fabric-loader:${fabric_loader_version}"

    implementation 'org.mariuszgromada.math:MathParser.org-mXparser:5.2.1'

    modApi "com.terraformersmc:modmenu:${mod_menu_version}"

    modApi("me.shedaniel.cloth:cloth-config-fabric:${cloth_version}") {
        //noinspection GroovyAssignabilityCheck
        exclude(group: "net.fabricmc.fabric-api")
    }

    modCompileOnlyApi("mezz.jei:jei-${minecraft_version}-common-api:${jei_version}")

    modCompileOnly "me.shedaniel:RoughlyEnoughItems-api:${rei_version}"
    modCompileOnly "me.shedaniel:RoughlyEnoughItems-default-plugin:${rei_version}"

    modCompileOnly "earth.terrarium.adastra:ad_astra-common-$minecraft_version:${ad_astra_version}"
    // modRuntimeOnly "earth.terrarium.adastra:ad_astra-common-$minecraft_version:${ad_astra_version}"

    // Create mod and Create Big Cannons dependencies
    modCompileOnly "curse.maven:create-328085:${create_version}"
    modCompileOnly "curse.maven:create-big-cannons-646668:${create_big_cannons_version}"
}

publishing {
    publications {
        mavenCommon(MavenPublication) {
            artifactId = rootProject.archives_base_name
            groupId = rootProject.group_id
            version = version + "+common"
            from components.java
        }
    }
}